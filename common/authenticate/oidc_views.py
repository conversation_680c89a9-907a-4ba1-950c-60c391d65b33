from mozilla_django_oidc.views import OIDCAuthenticationCallbackView
from django.http import JsonResponse
from django.contrib.auth import login
from django.shortcuts import redirect
from django.urls import reverse
import logging

logger = logging.getLogger("mozilla_django_oidc")


class FeishuOIDCCallbackView(OIDCAuthenticationCallbackView):
    """
    自定义飞书 OIDC 回调视图，提供更好的错误处理和调试信息
    """
    
    def get(self, request):
        """处理 OIDC 回调"""
        logger.debug(f"OIDC callback received with params: {request.GET}")
        
        # 检查是否有错误参数
        if 'error' in request.GET:
            error = request.GET.get('error')
            error_description = request.GET.get('error_description', '')
            logger.error(f"OIDC callback error: {error} - {error_description}")
            
            # 返回错误页面或重定向到登录页面
            return redirect(f"{reverse('sql:login')}?error=oidc_error&message={error}")
        
        # 检查是否有授权码
        if 'code' not in request.GET:
            logger.error("OIDC callback missing authorization code")
            return redirect(f"{reverse('sql:login')}?error=missing_code")
        
        try:
            # 调用父类的处理方法
            response = super().get(request)
            logger.info("OIDC authentication successful")
            return response
        except Exception as e:
            logger.error(f"OIDC authentication failed: {e}")
            return redirect(f"{reverse('sql:login')}?error=auth_failed&message={str(e)}")
    
    def login_failure(self):
        """登录失败时的处理"""
        logger.error("OIDC login failure")
        return redirect(f"{reverse('sql:login')}?error=login_failed")
    
    def login_success(self):
        """登录成功时的处理"""
        logger.info(f"User {self.user} logged in successfully via OIDC")
        return super().login_success()
