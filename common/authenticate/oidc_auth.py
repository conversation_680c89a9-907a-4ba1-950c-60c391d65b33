from mozilla_django_oidc import auth
from django.core.exceptions import SuspiciousOperation
from common.auth import init_user


class OIDCAuthenticationBackend(auth.OIDCAuthenticationBackend):
    def create_user(self, claims):
        """Return object for a newly created user account."""
        email = claims.get("email")
        # 飞书 OIDC 可能使用 email 作为用户名，或者使用 sub 字段
        username = claims.get("preferred_username") or claims.get("email") or claims.get("sub")
        display = claims.get("name")

        if not email:
            raise SuspiciousOperation("email should not be empty")
        if not username:
            raise SuspiciousOperation("username (preferred_username, email, or sub) should not be empty")
        if not display:
            raise SuspiciousOperation("name should not be empty")

        # 如果用户名是邮箱格式，提取邮箱的用户名部分
        if "@" in username:
            username = username.split("@")[0]

        user = self.UserModel.objects.create_user(
            username, email=email
        )
        user.display = display
        user.save()
        init_user(user)
        return user

    def describe_user_by_claims(self, claims):
        username = claims.get("preferred_username") or claims.get("email") or claims.get("sub")
        return "username {}".format(username)

    def filter_users_by_claims(self, claims):
        """Return all users matching the username."""
        username = claims.get("preferred_username") or claims.get("email") or claims.get("sub")
        if not username:
            return self.UserModel.objects.none()

        # 如果用户名是邮箱格式，提取邮箱的用户名部分
        if "@" in username:
            username = username.split("@")[0]

        if username == "admin":
            return self.UserModel.objects.none()
        return self.UserModel.objects.filter(username__iexact=username)
