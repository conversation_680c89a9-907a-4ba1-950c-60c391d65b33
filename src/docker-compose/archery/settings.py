# -*- coding: UTF-8 -*-


# 在这里写配置可以覆盖 archery/settings.py 内的配置
# DATABASES = {}

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
SECURE_SSL_REDIRECT = True  # 将所有非SSL请求永久重定向到SSL
SESSION_COOKIE_SECURE = True  # 仅通过https传输cookie
CSRF_COOKIE_SECURE = True  # 仅通过https传输cookie
SECURE_HSTS_INCLUDE_SUBDOMAINS = True  # 严格要求使用https协议传输
SECURE_HSTS_PRELOAD = True
SECURE_HSTS_SECONDS = 60
SECURE_CONTENT_TYPE_NOSNIFF = True  # 防止浏览器猜测资产的内容类型
CSRF_TRUSTED_ORIGINS = ["https://lark-archery-callback-test.insta360.cn"]
CORS_ORIGIN_WHITELIST = ("https://lark-archery-callback-test.insta360.cn",)